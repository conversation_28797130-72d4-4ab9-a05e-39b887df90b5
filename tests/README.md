# PDF Invoice Parser - Test Suite

This directory contains simplified tests for the PDF Invoice Parser application.

## Test Structure

```
tests/
├── enhanced-parser.test.js  # Core parser functionality tests
├── run-tests.js            # Test runner utility
└── README.md               # This file
```

## Test Categories

### 🔧 Enhanced Parser Tests (`enhanced-parser.test.js`)
Tests core parsing functionality:
- Document type detection (Delivery Challan, Tax Invoice, Job Order, Purchase Order)
- Company information extraction
- Basic data structure validation
- Edge case handling (empty text, whitespace)
- Raw text preservation

**Run with:** `node tests/enhanced-parser.test.js`

## How to Run Tests

### Enhanced Parser Tests

```bash
# Run the core parser tests
node tests/enhanced-parser.test.js

# Or use the test runner utility
node tests/run-tests.js
```

### Manual UI Testing

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Open http://localhost:3001 in your browser

3. Test the split-screen PDF parser:
   - Upload a PDF from the `example file` folder
   - Verify split-screen layout appears
   - Test PDF viewer responsiveness when resizing panels
   - Test data editing in UI view mode
   - Test raw JSON view mode
   - Test "Add New PDF" functionality

## Test Data

### Sample Documents (in `example file` folder)
- **RSNT26J0022 - Resonate.pdf**: B2C Job Order document
- **RSNT26D0127 - Ingram 32.pdf**: Delivery Challan document
- **RSNT26T0122 - Diligent Solutions.pdf**: Tax Invoice document
- **IAPO_66-G3474_20250718_141420.pdf**: Purchase Order document

## Manual Testing Checklist

### 📋 Core Functionality Tests
- [ ] Upload PDF files from `example file` folder
- [ ] Verify split-screen layout appears correctly
- [ ] Check PDF viewer displays document properly
- [ ] Verify parsed data appears in right panel
- [ ] Test document type detection accuracy
- [ ] Check company information extraction

### 🎨 UI Functionality Tests
- [ ] Test split panel resizing (drag divider)
- [ ] Test PDF zoom controls (in/out/reset)
- [ ] Test view mode toggle (UI/Raw JSON)
- [ ] Test field editing in UI view
- [ ] Test "Add New PDF" button functionality
- [ ] Test responsive design on different screen sizes

### 🔧 Parser Functionality Tests
- [ ] Test different document types (Delivery Challan, Tax Invoice, Job Order, Purchase Order)
- [ ] Verify field extraction accuracy
- [ ] Check handling of missing or malformed data
- [ ] Test edge cases (empty PDFs, corrupted files)

## Common Issues and Solutions

### Issue: Tests fail with "Cannot find module"
**Solution:** Ensure you're running tests from the project root directory

### Issue: Development server not starting
**Solution:** Check if port 3001 is available, or use `npm run dev` to start the server

### Issue: PDF not displaying in viewer
**Solution:** Ensure the PDF file is valid and not corrupted

## Adding New Tests

### For Parser Tests:
1. Add test cases to `tests/enhanced-parser.test.js`
2. Follow the existing pattern with `runner.test()` calls
3. Use the `expect()` helper for assertions

### For Manual Tests:
1. Add new test cases to the manual testing checklist above
2. Test complete user workflows
3. Include edge cases and error scenarios

## Test Coverage Goals

- ✅ **Document Type Detection**: All supported types (Delivery Challan, Tax Invoice, Job Order, Purchase Order)
- ✅ **Split-Screen UI**: Responsive layout and panel resizing
- ✅ **PDF Viewer**: Display, zoom, and navigation functionality
- ✅ **Data Editing**: UI view mode with field editing capabilities
- ✅ **Error Handling**: Graceful handling of invalid files and edge cases
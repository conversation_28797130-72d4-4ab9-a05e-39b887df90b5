#!/usr/bin/env node

/**
 * Test Runner for PDF Invoice Parser
 * Runs unit and integration tests
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Test results tracking
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;
const testResults = [];

// Mock test framework functions
global.describe = function(description, testSuite) {
  console.log(`\n${colors.blue}${colors.bright}📋 ${description}${colors.reset}`);
  testSuite();
};

global.test = function(description, testFunction) {
  totalTests++;
  try {
    testFunction();
    passedTests++;
    console.log(`  ${colors.green}✅ ${description}${colors.reset}`);
    testResults.push({ description, status: 'PASS' });
  } catch (error) {
    failedTests++;
    console.log(`  ${colors.red}❌ ${description}${colors.reset}`);
    console.log(`     ${colors.red}Error: ${error.message}${colors.reset}`);
    testResults.push({ description, status: 'FAIL', error: error.message });
  }
};

global.expect = function(actual) {
  return {
    toBe: function(expected) {
      if (actual !== expected) {
        throw new Error(`Expected ${expected}, but got ${actual}`);
      }
    },
    toEqual: function(expected) {
      if (JSON.stringify(actual) !== JSON.stringify(expected)) {
        throw new Error(`Expected ${JSON.stringify(expected)}, but got ${JSON.stringify(actual)}`);
      }
    },
    toBeUndefined: function() {
      if (actual !== undefined) {
        throw new Error(`Expected undefined, but got ${actual}`);
      }
    },
    toBeDefined: function() {
      if (actual === undefined) {
        throw new Error(`Expected value to be defined, but got undefined`);
      }
    },
    toHaveLength: function(expected) {
      if (!actual || actual.length !== expected) {
        throw new Error(`Expected length ${expected}, but got ${actual ? actual.length : 'undefined'}`);
      }
    },
    toContain: function(expected) {
      if (!actual || !actual.includes(expected)) {
        throw new Error(`Expected array to contain ${expected}`);
      }
    },
    toBeGreaterThan: function(expected) {
      if (actual <= expected) {
        throw new Error(`Expected ${actual} to be greater than ${expected}`);
      }
    },
    toBeLessThanOrEqual: function(expected) {
      if (actual > expected) {
        throw new Error(`Expected ${actual} to be less than or equal to ${expected}`);
      }
    }
  };
};

// Function to run a test file
function runTestFile(filePath) {
  console.log(`\n${colors.cyan}${colors.bright}🧪 Running ${path.basename(filePath)}${colors.reset}`);
  console.log(`${colors.cyan}${'='.repeat(50)}${colors.reset}`);
  
  try {
    // Clear require cache to ensure fresh module loading
    delete require.cache[require.resolve(filePath)];
    require(filePath);
  } catch (error) {
    console.log(`${colors.red}❌ Failed to load test file: ${error.message}${colors.reset}`);
    failedTests++;
  }
}

// Function to display test summary
function displaySummary() {
  console.log(`\n${colors.bright}${'='.repeat(60)}${colors.reset}`);
  console.log(`${colors.bright}📊 TEST SUMMARY${colors.reset}`);
  console.log(`${colors.bright}${'='.repeat(60)}${colors.reset}`);
  
  console.log(`${colors.bright}Total Tests:${colors.reset} ${totalTests}`);
  console.log(`${colors.green}${colors.bright}Passed:${colors.reset} ${passedTests}`);
  console.log(`${colors.red}${colors.bright}Failed:${colors.reset} ${failedTests}`);
  
  const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
  console.log(`${colors.bright}Success Rate:${colors.reset} ${successRate}%`);
  
  if (failedTests > 0) {
    console.log(`\n${colors.red}${colors.bright}❌ FAILED TESTS:${colors.reset}`);
    testResults
      .filter(result => result.status === 'FAIL')
      .forEach(result => {
        console.log(`  ${colors.red}• ${result.description}${colors.reset}`);
        if (result.error) {
          console.log(`    ${colors.red}${result.error}${colors.reset}`);
        }
      });
  }
  
  console.log(`\n${colors.bright}${'='.repeat(60)}${colors.reset}`);
  
  if (failedTests === 0) {
    console.log(`${colors.green}${colors.bright}🎉 All tests passed!${colors.reset}`);
    process.exit(0);
  } else {
    console.log(`${colors.red}${colors.bright}💥 ${failedTests} test(s) failed!${colors.reset}`);
    process.exit(1);
  }
}

// Main test runner function
function runTests() {
  console.log(`${colors.magenta}${colors.bright}🚀 PDF Invoice Parser Test Suite${colors.reset}`);
  console.log(`${colors.magenta}Starting test execution...${colors.reset}`);

  const testDir = __dirname;
  const testFiles = [];

  // Find the enhanced parser test file
  const enhancedParserTest = path.join(testDir, 'enhanced-parser.test.js');

  if (fs.existsSync(enhancedParserTest)) {
    testFiles.push(enhancedParserTest);
  }

  if (testFiles.length === 0) {
    console.log(`${colors.yellow}⚠️  No test files found!${colors.reset}`);
    console.log(`${colors.yellow}Make sure enhanced-parser.test.js exists in the tests directory${colors.reset}`);
    process.exit(1);
  }

  console.log(`${colors.bright}Found ${testFiles.length} test file(s)${colors.reset}`);

  // Run each test file
  testFiles.forEach(runTestFile);

  // Display summary
  displaySummary();
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
${colors.bright}PDF Invoice Parser Test Runner${colors.reset}

Usage: node tests/run-tests.js [options]

Options:
  --help, -h     Show this help message
  --unit         Run only unit tests
  --integration  Run only integration tests
  --verbose, -v  Verbose output

Examples:
  node tests/run-tests.js              # Run all tests
  node tests/run-tests.js --unit       # Run only unit tests
  node tests/run-tests.js --integration # Run only integration tests
`);
  process.exit(0);
}

// Check if specific test type is requested
if (args.includes('--unit')) {
  console.log(`${colors.bright}Running unit tests only...${colors.reset}`);
  // Modify test file discovery to only include unit tests
}

if (args.includes('--integration')) {
  console.log(`${colors.bright}Running integration tests only...${colors.reset}`);
  // Modify test file discovery to only include integration tests
}

// Start test execution
runTests();
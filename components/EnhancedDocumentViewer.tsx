'use client';

import React, { useState } from 'react';
import { ParsedDocumentData } from '@/lib/optimized-pdf-parser';
import JSONExporter from './JSONExporter';

interface EnhancedDocumentViewerProps {
  data: ParsedDocumentData | null;
  filename?: string;
}

export default function EnhancedDocumentViewer({ data, filename }: EnhancedDocumentViewerProps) {
  const [activeTab, setActiveTab] = useState<'structured' | 'json' | 'export'>('structured');

  const renderStructuredView = () => {
    if (!data) return null;

    // Arcsys Invoice
    if ('InvoiceNo' in data) {
      return (
        <div className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Invoice Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium">Invoice No:</span> {data.InvoiceNo}</div>
              <div><span className="font-medium">Date:</span> {data.InvoiceDate}</div>
              <div><span className="font-medium">Delivery Note:</span> {data.DeliveryNote}</div>
              <div><span className="font-medium">Delivery Date:</span> {data.DeliveryNoteDate}</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">Seller</h4>
              <div className="text-sm space-y-1">
                <div><span className="font-medium">Name:</span> {data.Seller?.Name}</div>
                <div><span className="font-medium">GSTIN:</span> {data.Seller?.GSTIN}</div>
                <div><span className="font-medium">PAN:</span> {data.Seller?.PAN}</div>
                <div><span className="font-medium">Email:</span> {data.Seller?.Email}</div>
              </div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <h4 className="font-semibold text-orange-900 mb-2">Buyer</h4>
              <div className="text-sm space-y-1">
                <div><span className="font-medium">Name:</span> {data.Buyer?.Name}</div>
                <div><span className="font-medium">GSTIN:</span> {data.Buyer?.GSTIN}</div>
                <div><span className="font-medium">PAN:</span> {data.Buyer?.PAN}</div>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-900 mb-2">Items</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr className="border-b border-purple-200">
                    <th className="text-left py-2">Description</th>
                    <th className="text-left py-2">HSN/SAC</th>
                    <th className="text-right py-2">Qty</th>
                    <th className="text-right py-2">Rate</th>
                    <th className="text-right py-2">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {data.Items?.map((item, index) => (
                    <tr key={index} className="border-b border-purple-100">
                      <td className="py-2">{item.Description}</td>
                      <td className="py-2">{item['HSN/SAC']}</td>
                      <td className="text-right py-2">{item.Quantity}</td>
                      <td className="text-right py-2">₹{item.Rate}</td>
                      <td className="text-right py-2">₹{item.Amount}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="bg-red-50 p-4 rounded-lg">
            <h4 className="font-semibold text-red-900 mb-2">Tax & Total</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium">IGST:</span> ₹{data.Tax?.IGST?.Amount} ({data.Tax?.IGST?.Rate})</div>
              <div><span className="font-medium">Total Amount:</span> ₹{data.TotalAmount}</div>
            </div>
          </div>
        </div>
      );
    }

    // Purchase Order
    if ('PurchaseOrderNo' in data && 'Supplier' in data) {
      return (
        <div className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Purchase Order Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium">PO No:</span> {data.PurchaseOrderNo}</div>
              <div><span className="font-medium">Date:</span> {data.Date}</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">Buyer</h4>
              <div className="text-sm space-y-1">
                <div><span className="font-medium">Name:</span> {data.Buyer?.Name}</div>
                <div><span className="font-medium">GSTIN:</span> {data.Buyer?.GSTIN}</div>
                <div><span className="font-medium">Email:</span> {data.Buyer?.Email}</div>
              </div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <h4 className="font-semibold text-orange-900 mb-2">Supplier</h4>
              <div className="text-sm space-y-1">
                <div><span className="font-medium">Name:</span> {data.Supplier?.Name}</div>
                <div><span className="font-medium">GSTIN:</span> {data.Supplier?.GSTIN}</div>
                <div><span className="font-medium">PAN:</span> {data.Supplier?.PAN}</div>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-900 mb-2">Items</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr className="border-b border-purple-200">
                    <th className="text-left py-2">Description</th>
                    <th className="text-right py-2">Qty</th>
                    <th className="text-right py-2">Rate</th>
                    <th className="text-right py-2">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {data.Items?.map((item, index) => (
                    <tr key={index} className="border-b border-purple-100">
                      <td className="py-2">{item.Description}</td>
                      <td className="text-right py-2">{item.Quantity}</td>
                      <td className="text-right py-2">₹{item.Rate}</td>
                      <td className="text-right py-2">₹{item.Amount}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="bg-red-50 p-4 rounded-lg">
            <h4 className="font-semibold text-red-900 mb-2">Tax & Total</h4>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div><span className="font-medium">CGST:</span> ₹{data.Taxes?.CGST}</div>
              <div><span className="font-medium">SGST:</span> ₹{data.Taxes?.SGST}</div>
              <div><span className="font-medium">Total:</span> ₹{data.TotalAmount}</div>
            </div>
          </div>
        </div>
      );
    }

    // Delivery Note
    if ('DeliveryNoteNo' in data && 'Seller' in data) {
      return (
        <div className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Delivery Note Information</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><span className="font-medium">Delivery Note No:</span> {data.DeliveryNoteNo}</div>
              <div><span className="font-medium">Date:</span> {data.Date}</div>
              <div><span className="font-medium">Total Quantity:</span> {data.TotalQuantity}</div>
              <div><span className="font-medium">Remarks:</span> {data.Remarks}</div>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-900 mb-2">Items</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr className="border-b border-purple-200">
                    <th className="text-left py-2">Description</th>
                    <th className="text-left py-2">HSN/SAC</th>
                    <th className="text-right py-2">Quantity</th>
                    <th className="text-left py-2">Unit</th>
                  </tr>
                </thead>
                <tbody>
                  {data.Items?.map((item, index) => (
                    <tr key={index} className="border-b border-purple-100">
                      <td className="py-2">{item.Description}</td>
                      <td className="py-2">{item['HSN/SAC']}</td>
                      <td className="text-right py-2">{item.Quantity}</td>
                      <td className="py-2">{item.Unit}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      );
    }

    // Generic view for other document types
    return (
      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Document Information</h3>
          <div className="grid grid-cols-1 gap-2 text-sm">
            {Object.entries(data).map(([key, value]) => (
              <div key={key} className="flex">
                <span className="font-medium w-1/3">{key}:</span>
                <span className="w-2/3">
                  {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  if (!data) {
    return (
      <div className="bg-gray-50 rounded-lg p-8 text-center">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Document Parsed</h3>
        <p className="text-gray-500">Upload a PDF file to see the parsed data and export options</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          <button
            onClick={() => setActiveTab('structured')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'structured'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Structured View
          </button>
          <button
            onClick={() => setActiveTab('json')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'json'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Raw JSON
          </button>
          <button
            onClick={() => setActiveTab('export')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'export'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Export
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'structured' && renderStructuredView()}
        
        {activeTab === 'json' && (
          <div className="bg-gray-50 rounded-lg p-4">
            <pre className="text-sm text-gray-800 whitespace-pre-wrap overflow-auto max-h-96">
              {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        )}
        
        {activeTab === 'export' && (
          <JSONExporter data={data} filename={filename} />
        )}
      </div>
    </div>
  );
}

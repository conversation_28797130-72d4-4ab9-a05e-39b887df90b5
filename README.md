# Smart PDF Invoice Parser & Editor

A powerful web application built with Next.js 14 and React 18 that intelligently processes PDF invoices, extracts structured data, identifies missing fields, and provides an editable invoice interface. Transform any PDF invoice into structured, editable data with automated field detection and smart suggestions.

## ✨ Key Features

### 🧠 Intelligent Invoice Processing
- **Smart Field Extraction**: Automatically detects and extracts invoice fields (numbers, dates, amounts, vendor/customer info)
- **Missing Field Detection**: Identifies incomplete data and highlights required fields
- **Auto-Suggestions**: Provides intelligent suggestions for missing or unclear data
- **Confidence Scoring**: Shows parsing confidence and data quality metrics

### 📝 Interactive Invoice Editor
- **Web-Based Invoice View**: Display invoices in a clean, professional web format
- **Real-Time Editing**: Edit any field directly in the interface with instant validation
- **Line Item Management**: Add, edit, or remove invoice line items with automatic calculations
- **Auto-Calculations**: Automatically calculates totals, taxes, and subtotals

### 💾 Export & Data Management
- **Multiple Export Formats**: Export to JSON, CSV, or TXT formats
- **Structured Data**: Clean, organized data ready for accounting systems
- **Copy Functionality**: Quick copy-to-clipboard for any text content

### 🎨 Modern User Experience
- **Dual View Modes**: Switch between structured invoice view and raw text view
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Real-time Feedback**: Visual indicators for missing fields and suggestions
- **Professional UI**: Clean, modern interface built with Tailwind CSS

## 🚀 Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd pdf-invoice-parser
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Building for Production

```bash
npm run build
npm start
```

## 📁 Project Structure

```
pdf-invoice-parser/
├── app/                    # Next.js App Router pages
│   ├── api/
│   │   └── parse-pdf/     # PDF parsing API endpoint
│   ├── layout.tsx         # Root layout component
│   ├── page.tsx          # Home page
│   └── globals.css       # Global styles
├── components/            # React components
│   └── FileUpload.tsx    # File upload component
├── types/                # TypeScript type definitions
│   └── pdf-parse.d.ts    # pdf-parse library types
├── public/               # Static assets
├── next.config.js        # Next.js configuration
├── tailwind.config.js    # Tailwind CSS configuration
├── tsconfig.json         # TypeScript configuration
└── package.json          # Dependencies and scripts
```

## 🔧 Configuration

### Next.js Configuration

The project includes specific configuration for handling the pdf-parse library:

```javascript
// next.config.js
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['pdf-parse']
  }
};
```

This ensures that the pdf-parse library is treated as an external package during server-side rendering.

## 📚 API Reference

### POST /api/parse-pdf

Parses an uploaded PDF file and returns extracted text content.

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body: FormData with 'file' field containing PDF file

**Response:**
```json
{
  "text": "Extracted text content...",
  "numPages": 5,
  "info": {
    "PDFFormatVersion": "1.4",
    "IsAcroFormPresent": false,
    "IsXFAPresent": false
  }
}
```

**Error Response:**
```json
{
  "error": "Error message",
  "details": "Detailed error information"
}
```

## 🛠️ Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Testing the API

A test script is included to verify the API functionality:

```bash
node test-pdf-api.js
```

## 🔍 Troubleshooting

### Common Issues

1. **Font Loading Errors**: The project uses Inter font from Google Fonts. If you encounter font loading issues, ensure you have a stable internet connection.

2. **PDF Parsing Errors**: If PDF parsing fails, check that:
   - The uploaded file is a valid PDF
   - The PDF is not password-protected
   - The file size is reasonable (< 10MB recommended)

3. **Build Errors**: If you encounter build errors:
   - Delete `.next` folder and rebuild
   - Clear node_modules and reinstall dependencies
   - Check TypeScript errors in the terminal

### Dependency Issues

If you encounter module resolution issues:

```bash
# Clean install
rm -rf node_modules package-lock.json
npm install
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms

The application can be deployed to any platform that supports Node.js applications:
- Netlify
- Railway
- Heroku
- AWS
- Google Cloud Platform

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

If you encounter any issues or have questions, please create an issue in the repository.

## 🔧 Technical Details

### Dependencies Fixed

The following issues were resolved during setup:

1. **Version Compatibility**: Updated Next.js to 14.2.0 and React to 18.3.0 for compatibility
2. **Font Issues**: Replaced Geist font with Inter font for better compatibility
3. **TypeScript Errors**: Fixed error handling in API routes for proper TypeScript compliance
4. **Build Configuration**: Configured Next.js to properly handle the pdf-parse library

### Project Status

✅ **Working Features:**
- PDF file upload and parsing
- Text extraction from PDF files
- Error handling and user feedback
- Responsive design
- TypeScript support
- Production build

✅ **Tested Components:**
- API endpoint functionality
- File upload component
- Error handling
- Build process

The application is now fully functional and ready for use!